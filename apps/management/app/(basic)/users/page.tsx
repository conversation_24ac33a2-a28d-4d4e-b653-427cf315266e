"use client"

import { useState } from "react"
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Trash2, User } from "lucide-react"
import { But<PERSON> } from "@ragtop-web/ui/components/button"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@ragtop-web/ui/components/alert-dialog"
import { UserForm } from "./components/user-form"
import { CustomDrawer } from "@ragtop-web/ui/components/custom-drawer"
import { CustomContainer } from "@ragtop-web/ui/components/custom-container"
import { PaginatedTable } from "@ragtop-web/ui/components/paginated-table"
import { usePagination } from "@ragtop-web/ui/hooks/use-pagination"

import type {ColumnDef} from "@ragtop-web/ui/components/data-table"
import { useCreateUser, useDeleteUser, useUpdateUser, useUsers } from "@/service/user-service"
import { formatDateTime } from "@/lib/utils"

// 用户数据结构
export interface UserList {
  id: string
  login_name?: string
  name?: string
  create_time?: string
}

// 创建用户
export interface CreateUser {
  login_name: string
  password?: string
}

// 编辑用户
export interface UpdateUser {
  user_id: string
  login_name: string
  password?: string
}

// 统一类型，根据是否有 user_id 区分创建和编辑
export type User = CreateUser | UpdateUser

export default function UsersPage() {
  const [isDrawerOpen, setIsDrawerOpen] = useState(false)
  const [selectedUser, setSelectedUser] = useState<UserList | null>(null)
  const [isCreating, setIsCreating] = useState(false)
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)
  const [userToDelete, setUserToDelete] = useState<UserList | null>(null)

  // 使用分页Hook管理分页状态
  const {
    pageNumber,
    pageSize,
    handlePageChange,
    handlePageSizeChange,
  } = usePagination({
    initialPageSize: 10,
  })

  // 使用分页参数获取用户数据
  const { data, isLoading, isFetching } = useUsers(pageNumber, pageSize)

  const createMutate = useCreateUser()
  const updateMutate = useUpdateUser()
  const deleteMutate = useDeleteUser()

  // 加载状态
  const isTableLoading = isLoading || isFetching

  // 处理打开用户详情抽屉
  const handleEditUser = (user: UserList) => {
    setSelectedUser(user)
    setIsCreating(false)
    setIsDrawerOpen(true)
  }

  // 处理打开创建用户抽屉
  const handleCreateUser = () => {
    setSelectedUser(null)
    setIsCreating(true)
    setIsDrawerOpen(true)
  }

  // 处理关闭抽屉
  const handleCloseDrawer = () => {
    setIsDrawerOpen(false)
  }

  // 处理保存用户
  const handleSaveUser = (_user: User) => {
    if (isCreating) {
      // 创建用户
      createMutate.mutate(_user as CreateUser, {
        onSuccess: () => {
          setSelectedUser(null)
          setIsDrawerOpen(false)
        }
      })
    } else {
      // 编辑用户 - 确保有 user_id
      const updateData = {
        ..._user,
        user_id: selectedUser?.id || ''
      } as UpdateUser

      updateMutate.mutate(updateData, {
        onSuccess: () => {
          setSelectedUser(null)
          setIsDrawerOpen(false)
        }
      })
    }
  }

  // 处理打开删除确认对话框
  const handleOpenDeleteDialog = (user: UserList) => {
    setUserToDelete(user)
    setIsDeleteDialogOpen(true)
  }

  // 处理删除用户
  const handleDeleteUser = () => {
    deleteMutate.mutate({ user_id: userToDelete?.id || '' }, {
      onSuccess: () => {
        setIsDeleteDialogOpen(false)
        setUserToDelete(null)
      }
    })
  }

  const columns: ColumnDef<UserList>[] = [
    {
      accessorKey: "login_name",
      header: "账户名",
    },
    {
      accessorKey: "create_time",
      header: "创建时间",
      cell: ({row})=>{
        const createAt = row.getValue("create_time") || row.original.create_time
        if (!createAt) return "-"

        try {
          return formatDateTime(createAt as string)
        } catch (error) {
          return "-"
        }
      }
    },
    {
      accessorKey: "action",
      header: "操作",
      cell:({row})=>{
        const user = row.original
        return (
          <div className="flex items-center gap-2 transition-opacity">
            <Button
              variant="ghost"
              size="icon"
              onClick={() => handleEditUser(user)}
            >
              <Pencil className="h-4 w-4" />
              <span className="sr-only">编辑</span>
            </Button>
            <Button
              variant="ghost"
              size="icon"
              onClick={() => handleOpenDeleteDialog(user)}
            >
              <Trash2 className="h-4 w-4" />
              <span className="sr-only">删除</span>
            </Button>
          </div>
        )
      }
    },
  ]

  return (
    <CustomContainer
      title="账户管理"
      action={
        <Button onClick={handleCreateUser}>
          <PlusIcon className="h-4 w-4" />
          添加用户
        </Button>
      }>
      <PaginatedTable
        columns={columns}
        data={data}
        isLoading={isTableLoading}
        currentPage={pageNumber}
        pageSize={pageSize}
        onPageChange={handlePageChange}
        onPageSizeChange={handlePageSizeChange}
        showTotal={true}
        showPageSizeSelector={true}
      />

      {/* 用户抽屉 */}
      <CustomDrawer
        open={isDrawerOpen}
        onClose={handleCloseDrawer}
        title={isCreating ? "创建账户" : "编辑账户"}
      >
        <UserForm
          user={isCreating ? undefined : selectedUser!}
          onSave={handleSaveUser}
          isCreating={isCreating}
          isLoading={createMutate?.isPending || updateMutate?.isPending}
        />
      </CustomDrawer>

      {/* 删除确认对话框 */}
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>确认删除</AlertDialogTitle>
            <AlertDialogDescription>
              您确定要删除账户 "{userToDelete?.login_name}" 吗？此操作无法撤销。
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>取消</AlertDialogCancel>
            <AlertDialogAction onClick={handleDeleteUser}>
              删除
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </CustomContainer>

  )
}