# Markdown 测试文档

这是一个测试文档，用于验证 markdown 渲染器的代码块和行内代码显示。

## 代码块测试

### JavaScript 代码块
```javascript
function hello() {
  console.log("Hello, World!");
  return "success";
}

const data = {
  name: "test",
  value: 123
};
```

### Python 代码块
```python
def hello_world():
    print("Hello, World!")
    return True

# 这是注释
data = {"name": "test", "value": 123}
```

### TypeScript 代码块
```typescript
interface User {
  id: number;
  name: string;
}

const user: User = {
  id: 1,
  name: "<PERSON>"
};
```

## 行内代码测试

这里有一些行内代码：`console.log("test")`，还有 `const x = 42`，以及 `npm install`。

在句子中间使用行内代码：使用 `useState` hook 来管理状态，然后调用 `setCount(count + 1)` 来更新。

## 混合测试

你可以使用 `npm install` 命令安装依赖，然后运行以下代码：

```bash
npm start
npm run build
npm test
```

或者在 JavaScript 中：

```js
const result = await fetch('/api/data');
const json = await result.json();
```

这样就可以获取数据了。记住要使用 `try-catch` 来处理错误。
