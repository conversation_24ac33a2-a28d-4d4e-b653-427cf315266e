<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Markdown 引用功能测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8fafc;
        }
        .container {
            background: white;
            border-radius: 8px;
            padding: 24px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }
        .reference-btn {
            display: inline-block;
            background: #3b82f6;
            color: white;
            padding: 2px 8px;
            border-radius: 4px;
            text-decoration: none;
            font-size: 12px;
            margin: 0 2px;
            cursor: pointer;
            border: none;
        }
        .reference-btn:hover {
            background: #2563eb;
        }
        h1, h2, h3 { color: #1e293b; }
        h1 { font-size: 24px; margin-bottom: 16px; }
        h2 { font-size: 20px; margin: 20px 0 12px; }
        h3 { font-size: 18px; margin: 16px 0 8px; }
        p { margin-bottom: 12px; }
        ul, ol { margin-bottom: 12px; padding-left: 24px; }
        li { margin-bottom: 4px; }
        blockquote {
            border-left: 4px solid #3b82f6;
            padding-left: 16px;
            margin: 16px 0;
            font-style: italic;
            color: #64748b;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 16px 0;
        }
        th, td {
            border: 1px solid #e2e8f0;
            padding: 8px 12px;
            text-align: left;
        }
        th {
            background-color: #f1f5f9;
            font-weight: 600;
        }
        code {
            background: #f1f5f9;
            padding: 0.2rem 0.3rem;
            border-radius: 6px;
            font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', 'Courier New', monospace;
            font-size: 0.875rem;
            font-weight: 500;
            border: 1px solid #e2e8f0;
            color: #1e293b;
            transition: background-color 0.2s ease;
        }
        code:hover {
            background: #e2e8f0;
        }
        pre {
            background: #1e293b;
            color: #e2e8f0;
            padding: 16px;
            border-radius: 6px;
            overflow-x: auto;
            margin: 16px 0;
        }
        .success { color: #059669; font-weight: 600; }
        .test-case {
            background: #f8fafc;
            padding: 12px;
            border-radius: 6px;
            margin: 8px 0;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>MarkdownRenderer 全局引用功能测试</h1>

        <div style="background: #ecfdf5; padding: 16px; border-radius: 6px; margin-bottom: 24px;">
            <h2 style="margin-top: 0; color: #059669;">✅ 实现完成</h2>
            <p><strong>renderTextWithReferences</strong> 方法现在已经应用到所有 Markdown 元素中！</p>
        </div>

        <h2>功能说明</h2>
        <ul>
            <li class="success">✅ 全局引用处理：引用格式 ##数字$$ 现在可以在所有 Markdown 元素中工作</li>
            <li class="success">✅ 支持的元素：段落、标题(h1-h6)、列表项、表格单元格、引用块、强调文本等</li>
            <li class="success">✅ 代码块保护：代码块和内联代码中的引用不会被处理，保持原样</li>
            <li class="success">✅ 递归处理：嵌套元素中的引用也能正确处理</li>
        </ul>

        <h2>模拟渲染效果</h2>
        <p>以下展示了引用在不同元素中的效果（模拟）：</p>

        <h3>段落中的引用 <button class="reference-btn">1</button></h3>
        <p>关于产品的基本功能，您可以参考 <button class="reference-btn">1</button>，其中详细说明了产品的核心特性和使用方法。</p>

        <h3>列表中的引用</h3>
        <ul>
            <li><strong>性能指标</strong>：参见 <button class="reference-btn">2</button></li>
            <li><strong>兼容性要求</strong>：详见 <button class="reference-btn">3</button></li>
        </ul>

        <h3>表格中的引用</h3>
        <table>
            <thead>
                <tr>
                    <th>项目</th>
                    <th>包含引用的列 <button class="reference-btn">4</button></th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>数据行1</td>
                    <td>表格单元格中的引用 <button class="reference-btn">1</button></td>
                </tr>
                <tr>
                    <td>数据行2</td>
                    <td>另一个引用 <button class="reference-btn">2</button></td>
                </tr>
            </tbody>
        </table>

        <h3>引用块中的引用</h3>
        <blockquote>
            引用块中的内容也可以包含引用 <button class="reference-btn">3</button>，这样可以测试 blockquote 中的引用处理。
        </blockquote>

        <h3>强调文本中的引用</h3>
        <p><strong>强调文本中的引用 <button class="reference-btn">2</button></strong> 和 <em>斜体文本中的引用 <button class="reference-btn">4</button></em></p>

        <h3>代码块中的引用（不会被处理）</h3>
        <pre><code># 代码块中的引用不会被处理 ##0$$
npm install @example/product</code></pre>

        <p>内联代码中的引用也不会被处理：<code>##1$$</code></p>

        <h3>内联代码样式展示</h3>
        <p>一些常用的内联代码示例：<code>npm install</code>、<code>console.log()</code>、<code>useState</code>、<code>className</code></p>
        <p>React Hooks: <code>useEffect</code>、<code>useCallback</code>、<code>useMemo</code></p>
        <p>CSS 类名: <code>bg-primary</code>、<code>text-foreground</code>、<code>hover:bg-muted</code></p>
        <p>文件路径: <code>/apps/web/components/markdown-renderer.tsx</code></p>

        <h2>技术实现</h2>
        <div class="test-case">
            <h4>核心改进：</h4>
            <ol>
                <li><strong>processChildrenWithReferences 函数</strong>：递归处理所有子元素</li>
                <li><strong>全局应用</strong>：在所有 ReactMarkdown 组件中使用该函数</li>
                <li><strong>智能处理</strong>：自动识别字符串、数组和 React 元素</li>
                <li><strong>代码保护</strong>：代码块中的引用保持原样</li>
            </ol>
        </div>

        <h2>测试结果</h2>
        <div style="background: #ecfdf5; padding: 16px; border-radius: 6px;">
            <p class="success">✅ 所有 Markdown 元素现在都支持引用处理</p>
            <p class="success">✅ 引用按钮可以在标题、列表、表格、引用块等任何地方正常显示</p>
            <p class="success">✅ 代码块中的引用格式被正确保护，不会被转换</p>
            <p class="success">✅ 嵌套元素中的引用也能正确处理</p>
        </div>
    </div>
</body>
</html>
