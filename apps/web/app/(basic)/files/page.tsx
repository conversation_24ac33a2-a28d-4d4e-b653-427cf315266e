"use client"

import { useState} from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>eader, DialogTitle } from "@ragtop-web/ui/components/dialog"
import { FileList } from "./components/file-list"
import { FileUpload } from "./components/file-upload"
import { AddFileDropdown } from "./components/add-file-dropdown"
import { RenameFileDialog } from "./components/rename-file-dialog"

import { CustomContainer } from "@ragtop-web/ui/components/custom-container"
import { useToast } from "@ragtop-web/ui/components/use-toast"
import {
  useDeleteFile,
  useRenameFile,
  type FileItem,
} from "@/service"

/**
 * 文件管理页面
 */
// 2025.5.16: 本期先不做文件夹的支持，暂时隐藏面包屑，新增文件按钮改成单独的上传文件
export default function FilesPage() {
  const { toast } = useToast()
  const [isUploadDialogOpen, setIsUploadDialogOpen] = useState(false)
  const [selectedFile, setSelectedFile] = useState<FileItem | null>(null)
  const [isRenameDialogOpen, setIsRenameDialogOpen] = useState(false)

  const deleteFile = useDeleteFile()
  const renameFile = useRenameFile()

  // 处理文件删除
  const handleDeleteFile = (fileId: string) => {
    deleteFile.mutate(
      { file_id: fileId },
      {
        onSuccess: () => {
          toast({
            title: "删除成功",
            description: "文件已成功删除",
          })
        },
        onError: (error) => {
          toast({
            title: "删除失败",
            description: "删除文件时发生错误",
            variant: "destructive",
          })
        }
      }
    )
  }

  // 处理重命名
  const handleRenameFile = (file: FileItem) => {
    setSelectedFile(file)
    setIsRenameDialogOpen(true)
  }

  // 处理确认重命名
  const handleConfirmRename = (newName: string) => {
    if (!selectedFile) return

    renameFile.mutate(
      {
        file_id: selectedFile.id,
        name: newName
      },
      {
        onSuccess: () => {
          toast({
            title: "重命名成功",
            description: `文件已重命名为 ${newName}`,
          })
          setIsRenameDialogOpen(false)
        },
        onError: (error) => {
          toast({
            title: "重命名失败",
            description: "重命名文件时发生错误",
            variant: "destructive",
          })
        }
      }
    )
  }

  return (
    <CustomContainer
      title="文件管理"
      action={
        <AddFileDropdown
          onUploadFile={() => setIsUploadDialogOpen(true)}
        />
      }
    >

 
        <FileList
          onDeleteFile={handleDeleteFile}
          onFolderClick={()=>{}}
          onRenameFile={handleRenameFile}
        />

      {/* 上传文件对话框 */}
      <Dialog open={isUploadDialogOpen} onOpenChange={setIsUploadDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>上传文件</DialogTitle>
          </DialogHeader>
          <div className="p-4">
            <FileUpload />
          </div>
        </DialogContent>
      </Dialog>

      {/* 重命名对话框 */}
      {selectedFile && (
        <RenameFileDialog
          open={isRenameDialogOpen}
          onClose={() => setIsRenameDialogOpen(false)}
          onRename={handleConfirmRename}
          file={selectedFile}
        />
      )}
    </CustomContainer>
  )
}