"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { Play, Pencil, Search, Database, ChevronRight, ChevronDown, Table, Folder, FolderOpen } from "lucide-react"
import { But<PERSON> } from "@ragtop-web/ui/components/button"
import { Input } from "@ragtop-web/ui/components/input"
import { DataTable, type ColumnDef } from "@ragtop-web/ui/components/data-table"
import { Badge } from "@ragtop-web/ui/components/badge"
import { CustomContainer } from "@ragtop-web/ui/components/custom-container"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from "@ragtop-web/ui/components/dialog"
import { Textarea } from "@ragtop-web/ui/components/textarea"
import { Separator } from "@ragtop-web/ui/components/separator"
import { useToast } from "@ragtop-web/ui/components/use-toast"
import { formatDateTime } from "@/lib/utils"
import { cn } from "@ragtop-web/ui/lib/utils"
import {
  type Dataset,
  type DbTablesetRes,
  type DbTableColumn,
  useDataset,
  useDatasetSchemas,
  useDatasetTables,
  useDatasetDocuments,
  useUpdateDocument,
  useBuildDataset
} from "@/service/dataset-service"

// 本地接口定义
interface SchemaNode {
  id: string
  name: string
  tables: TableNode[]
  expanded: boolean
  loading: boolean
}

interface TableNode {
  id: string
  name: string
  schemaId: string
}

export default function DatasetDetails({ datasetId }: {
  datasetId: string
}) {
  const { toast } = useToast()
  const [searchTerm, setSearchTerm] = useState("")
  const [editingDocument, setEditingDocument] = useState<DbTableColumn | null>(null)
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)
  const [documentDescription, setDocumentDescription] = useState("")
  const [isInitialized, setIsInitialized] = useState(false)

  // 当前选中的表
  const [selectedTable, setSelectedTable] = useState<TableNode | null>(null)
  const [breadcrumbs, setBreadcrumbs] = useState<Array<{name: string, type: 'schema' | 'table'}>>([])

  // API hooks - 延迟加载
  const {data:dataset} = useDataset(datasetId)
  const { data: schemasData, isLoading: schemasLoading } = useDatasetSchemas(datasetId, 1, 100, isInitialized) // 获取所有schema
  const getTablesMutation = useDatasetTables()
  const getDocumentsMutation = useDatasetDocuments()
  const updateDocumentMutation = useUpdateDocument()
  const buildDatasetMutation = useBuildDataset()

  const [schemaNodes, setSchemaNodes] = useState<SchemaNode[]>([])
  const [documents, setDocuments] = useState<DbTableColumn[]>([])

  // 页面初始化时触发数据加载
  useEffect(() => {
    setIsInitialized(true)
  }, [])


  // 初始化schema节点
  useEffect(() => {
    if (schemasData?.records) {
      const nodes: SchemaNode[] = schemasData.records.map(schema => ({
        id: schema.schema_name || "",
        name: schema.schema_name || "",
        tables: [],
        expanded: false,
        loading: false
      }))
      setSchemaNodes(nodes)
    }
  }, [schemasData])

  // 处理schema展开/折叠
  const handleSchemaToggle = (schemaId: string) => {
    setSchemaNodes(prev => prev.map(node => {
      if (node.id === schemaId) {
        if (!node.expanded && node.tables.length === 0) {
          // 首次展开，需要加载表列表
          node.loading = true
          getTablesMutation.mutate({
            tableset_id: datasetId,
            schema_name: schemaId
          }, {
            onSuccess: (data) => {
              setSchemaNodes(current => current.map(n => {
                if (n.id === schemaId) {
                  return {
                    ...n,
                    tables: data.records?.map(table => ({
                      id: table.table_name || "",
                      name: table.table_name || "",
                      schemaId: schemaId
                    })) || [],
                    expanded: true,
                    loading: false
                  }
                }
                return n
              }))
            },
            onError: () => {
              toast({
                title: "错误",
                description: "获取表列表失败",
                variant: "destructive",
              })
              setSchemaNodes(current => current.map(n =>
                n.id === schemaId ? { ...n, loading: false } : n
              ))
            }
          })
        } else {
          // 切换展开状态
          return { ...node, expanded: !node.expanded }
        }
      }
      return node
    }))
  }

  // 处理表格选择
  const handleTableSelect = (table: TableNode) => {
    setSelectedTable(table)
    setBreadcrumbs([
      { name: table.schemaId, type: 'schema' },
      { name: table.name, type: 'table' }
    ])

    // 获取表格的列数据
    getDocumentsMutation.mutate({
      tableset_id: datasetId,
      schema_name: table.schemaId,
      table_name: table.name
    }, {
      onSuccess: (data) => {
        setDocuments(data)
      },
      onError: () => {
        toast({
          title: "错误",
          description: "获取表格列数据失败",
          variant: "destructive",
        })
      }
    })
  }

  // 过滤文档
  const filteredDocuments = documents.filter(doc => {
    if (!searchTerm) return true
    return (doc.column_name?.toLowerCase().includes(searchTerm.toLowerCase()) || false) ||
           (doc.comment?.toLowerCase().includes(searchTerm.toLowerCase()) || false)
  })

  // 处理构建数据集
  const handleBuildDataset = () => {
    if (!dataset?.id) return

    buildDatasetMutation.mutate({ tableset_id: dataset.id }, {
      onSuccess: () => {
        toast({
          title: "构建成功",
          description: "数据集已开始构建",
        })
      },
      onError: () => {
        toast({
          title: "构建失败",
          description: "构建数据集时发生错误",
          variant: "destructive",
        })
      }
    })
  }

  // 处理编辑文档
  const handleEditDocument = (document: DbTableColumn) => {
    setEditingDocument(document)
    setDocumentDescription(document.annotation || "")
    setIsEditDialogOpen(true)
  }

  // 保存文档描述
  const handleSaveDescription = () => {
    if (!editingDocument || !selectedTable) return

    updateDocumentMutation.mutate({
      tableset_id: datasetId,
      schema_name: selectedTable.schemaId,
      table_name: selectedTable.name,
      column_name: editingDocument.column_name || "",
      comment: documentDescription
    }, {
      onSuccess: () => {
        toast({
          title: "保存成功",
          description: "文档描述已更新",
        })
        setIsEditDialogOpen(false)
        setEditingDocument(null)

        // 重新获取文档数据
        if (selectedTable) {
          handleTableSelect(selectedTable)
        }
      },
      onError: () => {
        toast({
          title: "保存失败",
          description: "更新文档描述时发生错误",
          variant: "destructive",
        })
      }
    })
  }

  // 表格列定义
  const columns: ColumnDef<DbTableColumn>[] = [
    {
      accessorKey: "column_name",
      header: "Column Name",
    },
    {
      accessorKey: "data_type",
      header: "Column Type",
    },
    {
      accessorKey: "comment",
      header: "Comment",
    },
    {
      accessorKey: "annotation",
      header: "Document",
      cell: ({ row }) => {
        const document = row.original
        return (
          <div className="flex items-center justify-between">
            <div className="relative group">
              <div className="truncate max-w-[200px]">
                {document.annotation || "-"}
              </div>
              {document.annotation && (
                <div className="absolute left-0 top-0 z-50 invisible group-hover:visible bg-popover text-popover-foreground rounded-md p-2 shadow-md text-sm max-w-[300px] whitespace-normal break-words">
                  {document.annotation}
                </div>
              )}
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => handleEditDocument(document)}
            >
              <Pencil className="h-4 w-4" />
              <span className="sr-only">编辑</span>
            </Button>
          </div>
        )
      },
    },
  ]

  if (!dataset) {
    return (
      <CustomContainer title="数据集详情">
        <div className="flex items-center justify-center h-64">
          <p className="text-muted-foreground">加载中...</p>
        </div>
      </CustomContainer>
    )
  }

  // 构建面包屑导航
  const containerBreadcrumbs = [
    { title: "数据集列表", href: "/datasets", isCurrent: false },
    { title: dataset.name || "数据集详情", href: `/datasets/${datasetId}`, isCurrent: true }
  ]

  return (
    <CustomContainer
      title={dataset.name || "数据集详情"}
      breadcrumbs={containerBreadcrumbs}
      action={
        <Button onClick={handleBuildDataset} disabled={buildDatasetMutation.isPending || !!dataset.building_version}>
          {dataset.building_version ? (
            <>
              <div className="h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
              构建中
            </>
          ) : (
            <>
              <Play className="h-4 w-4" />
              构建
            </>
          )}
        </Button>
      }
    >
      {/* 数据集信息 */}
      <div className="flex items-center gap-6 text-sm text-muted-foreground mb-6 p-4 bg-muted/30 rounded-lg">
        <div className="flex items-center gap-2">
          <span className="font-medium">创建时间:</span>
          {dataset.create_time ? formatDateTime(new Date(dataset.create_time)) : "无"}
        </div>
        <div className="flex items-center gap-2">
          <span className="font-medium">数据库:</span>
          {dataset.meta?.database || "无"}
        </div>
        <div className="flex items-center gap-2">
          <span className="font-medium">数据构建版本:</span>
          {dataset.build_version || "无"}
        </div>
        {dataset.building_version && (
          <div className="flex items-center gap-2">
            <span className="font-medium">构建中版本:</span>
            <Badge variant="outline">{dataset.building_version}</Badge>
          </div>
        )}
      </div>

      {/* 左右布局 */}
      <div className="grid grid-cols-12 gap-6 h-[calc(100vh-300px)]">
        {/* 左侧树形结构 */}
        <div className="col-span-3 border-r pr-4">
          <div className="space-y-2">
            <h3 className="text-sm font-medium text-muted-foreground mb-4">数据库结构</h3>
            {schemasLoading ? (
              <div className="flex items-center justify-center h-32">
                <p className="text-sm text-muted-foreground">加载中...</p>
              </div>
            ) : (
              <div className="space-y-1">
                {schemaNodes.map((schema) => (
                  <div key={schema.id} className="space-y-1">
                    {/* Schema 节点 */}
                    <div
                      className="flex items-center gap-2 p-2 rounded-md hover:bg-muted/50 cursor-pointer"
                      onClick={() => handleSchemaToggle(schema.id)}
                    >
                      {schema.loading ? (
                        <div className="h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
                      ) : schema.expanded ? (
                        <ChevronDown className="h-4 w-4" />
                      ) : (
                        <ChevronRight className="h-4 w-4" />
                      )}
                      {schema.expanded ? (
                        <FolderOpen className="h-4 w-4 text-blue-500" />
                      ) : (
                        <Folder className="h-4 w-4 text-blue-500" />
                      )}
                      <span className="text-sm font-medium">{schema.name}</span>
                    </div>

                    {/* Tables 列表 */}
                    {schema.expanded && (
                      <div className="ml-6 space-y-1">
                        {schema.tables.map((table) => (
                          <div
                            key={table.id}
                            className={cn(
                              "flex items-center gap-2 p-2 rounded-md cursor-pointer text-sm",
                              selectedTable?.id === table.id
                                ? "bg-primary/10 text-primary"
                                : "hover:bg-muted/50"
                            )}
                            onClick={() => handleTableSelect(table)}
                          >
                            <Table className="h-4 w-4" />
                            <span>{table.name}</span>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>

        {/* 右侧内容区域 */}
        <div className="col-span-9 space-y-4">
          {selectedTable ? (
            <>
              {/* 面包屑导航 */}
              <div className="flex items-center gap-2 text-sm text-muted-foreground">
                <span>Schemas</span>
                <ChevronRight className="h-4 w-4" />
                <span>{breadcrumbs[0]?.name}</span>
                <ChevronRight className="h-4 w-4" />
                <span className="text-foreground font-medium">{breadcrumbs[1]?.name}</span>
              </div>

              {/* 表格标题 */}
              <div className="flex items-center gap-2">
                <Table className="h-5 w-5 text-primary" />
                <h2 className="text-lg font-medium">{selectedTable.name}</h2>
              </div>

              {/* 搜索栏 */}
              <div className="flex items-center gap-2">
                <Search className="h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="搜索列名或注释..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="max-w-sm"
                />
              </div>

              {/* 文档表格 */}
              <div className="border rounded-lg">
                <DataTable
                  columns={columns}
                  data={filteredDocuments}
                />
              </div>

              {/* 无数据提示 */}
              {filteredDocuments.length === 0 && (
                <div className="text-center py-8 text-muted-foreground">
                  {searchTerm ? "没有找到匹配的列" : "该表格没有列数据"}
                </div>
              )}
            </>
          ) : (
            <div className="flex items-center justify-center h-64 text-muted-foreground">
              <div className="text-center">
                <Database className="h-12 w-12 mx-auto mb-4 opacity-50" />
                <p>请从左侧选择一个表格查看列数据</p>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* 编辑文档对话框 */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>编辑文档</DialogTitle>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <h4 className="font-medium">列名: {editingDocument?.column_name}</h4>
              <p className="text-sm text-muted-foreground">
                {editingDocument?.comment}
              </p>
            </div>
            <Separator />
            <div className="space-y-2">
              <label htmlFor="description" className="text-sm font-medium">
                文档描述
              </label>
              <Textarea
                id="description"
                placeholder="输入文档描述..."
                value={documentDescription}
                onChange={(e) => setDocumentDescription(e.target.value)}
                rows={5}
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>
              取消
            </Button>
            <Button onClick={handleSaveDescription} disabled={updateDocumentMutation.isPending}>
              {updateDocumentMutation.isPending ? "保存中..." : "保存"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </CustomContainer>
  )
}
