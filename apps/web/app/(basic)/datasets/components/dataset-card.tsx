"use client"

import { useState } from "react"
import { Database, Play, Pencil } from "lucide-react"
import { Card, CardContent, CardHeader, CardTitle } from "@ragtop-web/ui/components/card"
import { Button } from "@ragtop-web/ui/components/button"
import { type Dataset } from "@/service/dataset-service"
import { formatDateTime } from "@/lib/utils"

interface DatasetCardProps {
  dataset: Dataset
  onClick: () => void
  onBuild?: (dataset: Dataset, event: React.MouseEvent) => void
  onEdit?: (dataset: Dataset, event: React.MouseEvent) => void
}

export function DatasetCard({ dataset, onClick, onBuild, onEdit }: DatasetCardProps) {
  const [isHovered, setIsHovered] = useState(false)

  const handleBuild = (e: React.MouseEvent) => {
    e.stopPropagation()
    if (onBuild) {
      onBuild(dataset, e)
    }
  }

  const handleEdit = (e: React.MouseEvent) => {
    e.stopPropagation()
    if (onEdit) {
      onEdit(dataset, e)
    }
  }

  return (
    <Card
      className="cursor-pointer hover:border-primary/50 transition-colors relative gap-4"
      onClick={onClick}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Database className="h-5 w-5 text-primary" />
          {dataset.name}
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="text-sm text-muted-foreground space-y-1">
          <p>
            <span className="font-medium">描述:</span> {dataset.description || "无"}
          </p>
          <p>
            <span className="font-medium">数据库:</span> {dataset.meta?.database || "无"}
          </p>
          <p>
            <span className="font-medium">数据构建版本:</span> {dataset.build_version || "无"}
          </p>
          {dataset.building_version && (
            <p>
              <span className="font-medium">构建中版本:</span> {dataset.building_version}
            </p>
          )}
          <p>
            <span className="font-medium">创建时间:</span> {dataset.create_time ? formatDateTime(new Date(dataset.create_time)) : "无"}
          </p>
        </div>

        {/* 按钮组 - 仅在悬停时显示 */}
        {isHovered && (
          <div className="absolute top-2 right-2 flex gap-2">
            <Button
              size="sm"
              variant="outline"
              className="h-8 px-2"
              onClick={handleEdit}
            >
              <Pencil className="h-4 w-4" />
              <span className="sr-only">编辑</span>
            </Button>

            <Button
              size="sm"
              variant="outline"
              className="h-8 px-2"
              onClick={handleBuild}
              disabled={!!dataset.building_version}
            >
              {dataset.building_version ? (
                <>
                  <div className="h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
                  构建中
                </>
              ) : (
                <>
                  <Play className="h-4 w-4" />
                  构建
                </>
              )}
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
