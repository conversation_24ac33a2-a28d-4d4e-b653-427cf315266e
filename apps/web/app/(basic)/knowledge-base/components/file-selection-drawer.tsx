"use client"

import { useState } from "react"
import { FileText, Check } from "lucide-react"
import { CustomDrawer } from "@ragtop-web/ui/components/custom-drawer"
import { Button } from "@ragtop-web/ui/components/button"
import { Badge } from "@ragtop-web/ui/components/badge"
import { Checkbox } from "@ragtop-web/ui/components/checkbox"
import { SelectionList, type SelectionItem } from "@ragtop-web/ui/components/selection-list"
import { useFiles, type FileItem } from "@/service/file-service"
import { formatDateTime } from "@/lib/utils"

interface FileSelectionDrawerProps {
  open: boolean
  onClose: () => void
  onConfirm: (selectedFileIds: string[]) => void
  kbaseId: string
  knowledgeBaseName: string
}

/**
 * 文件选择抽屉组件
 * 用于从文件列表中选择文件并添加到知识库
 */
export function FileSelectionDrawer({
  open,
  onClose,
  onConfirm,
  kbaseId,
  knowledgeBaseName,
}: FileSelectionDrawerProps) {
  const [selectedFileIds, setSelectedFileIds] = useState<string[]>([])

  // 由于 useFiles hook 的限制，我们暂时使用传统方式
  // 后续可以考虑重构 useFiles 以支持动态参数
  const [pageNumber, setPageNumber] = useState(1)
  const [pageSize] = useState(10)
  const [searchTerm, setSearchTerm] = useState("")

  // 获取文件列表
  const { data: filesData, isLoading } = useFiles(pageNumber, pageSize, kbaseId, "EXCLUDE")

  const totalItems = filesData?.total || 0
  const fileList = filesData?.records || []

  // 过滤文件（基于搜索词）
  const filteredFiles = fileList.filter((file: FileItem) =>
    file.name.toLowerCase().includes(searchTerm.toLowerCase())
  )

  // 转换数据为 SelectionItem 格式
  const selectionItems: SelectionItem[] = filteredFiles.map(file => ({
    id: file.id,
    label: file.name,
    disabled: false
  }))

  // 处理选择变化
  const handleSelectionChange = (selectedIds: string | string[]) => {
    if (Array.isArray(selectedIds)) {
      setSelectedFileIds(selectedIds)
    }
  }



  // 处理分页变化
  const handlePageChange = (page: number) => {
    setPageNumber(page)
  }

  // 处理确认选择
  const handleConfirm = () => {
    onConfirm(selectedFileIds)
    // 重置状态
    setSelectedFileIds([])
    setSearchTerm("")
    setPageNumber(1)
  }

  // 处理取消
  const handleCancel = () => {
    onClose()
    // 重置状态
    setSelectedFileIds([])
    setSearchTerm("")
    setPageNumber(1)
  }

  // 自定义渲染文件项
  const renderFileItem = (
    item: SelectionItem,
    isSelected: boolean,
    isDisabled: boolean,
    onSelect: (itemId: string) => void
  ) => {
    const file = filteredFiles.find(f => f.id === item.id)
    if (!file) return null

    return (
      <div
        key={item.id}
        className={`flex items-center p-3 rounded-md border cursor-pointer transition-colors ${
          isSelected
            ? "bg-primary/10 border-primary"
            : "hover:bg-muted border-border"
        } ${isDisabled ? "opacity-50 cursor-not-allowed" : ""}`}
        onClick={() => !isDisabled && onSelect(item.id)}
      >
        <Checkbox
          checked={isSelected}
          disabled={isDisabled}
          className="mr-3 pointer-events-none"
        />
        <div className="flex-1 min-w-0">
          <div className="flex items-center gap-2 mb-1">
            <FileText className="h-4 w-4 text-primary flex-shrink-0" />
            <span className="font-medium truncate">{file.name}</span>
          </div>
          <div className="text-xs text-muted-foreground">
            大小: {file.size} | 创建时间: {formatDateTime(new Date(Date.now()))}
          </div>
        </div>
      </div>
    )
  }

  return (
    <CustomDrawer
      open={open}
      onClose={handleCancel}
      title={`选择文件 - ${knowledgeBaseName}`}
      footer={
        <div className="flex items-center justify-between w-full">
          <div className="text-sm text-muted-foreground">
            已选择 {selectedFileIds.length} 个文件
          </div>
          <div className="flex gap-2">
            <Button variant="outline" onClick={handleCancel}>
              取消
            </Button>
            <Button
              onClick={handleConfirm}
              disabled={selectedFileIds.length === 0}
            >
              <Check className="h-4 w-4" />
              添加到知识库
            </Button>
          </div>
        </div>
      }
    >
      <div className="space-y-4">
        {/* 文件统计 */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <FileText className="h-5 w-5 text-primary" />
            <span className="text-sm font-medium">文件列表</span>
          </div>
          <Badge variant="outline">
            {totalItems} 个文件
          </Badge>
        </div>

        <SelectionList
          items={selectionItems}
          isLoading={isLoading}
          hasMore={false} // 使用分页而不是无限滚动
          searchTerm={searchTerm}
          onSearchChange={setSearchTerm}
          selectionMode="multiple"
          selectedIds={selectedFileIds}
          onSelectionChange={handleSelectionChange}
          searchPlaceholder="搜索文件..."
          emptyText="未找到匹配的文件"
          loadingText="加载中..."
          endText="已显示所有文件"
          height="400px"
          renderItem={renderFileItem}
        />

        {/* 分页 */}
        {totalItems > pageSize && (
          <div className="flex justify-center">
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => handlePageChange(Math.max(1, pageNumber - 1))}
                disabled={pageNumber <= 1}
              >
                上一页
              </Button>
              <span className="text-sm text-muted-foreground">
                第 {pageNumber} 页，共 {Math.ceil(totalItems / pageSize)} 页
              </span>
              <Button
                variant="outline"
                size="sm"
                onClick={() => handlePageChange(Math.min(Math.ceil(totalItems / pageSize), pageNumber + 1))}
                disabled={pageNumber >= Math.ceil(totalItems / pageSize)}
              >
                下一页
              </Button>
            </div>
          </div>
        )}
      </div>
    </CustomDrawer>
  )
}
