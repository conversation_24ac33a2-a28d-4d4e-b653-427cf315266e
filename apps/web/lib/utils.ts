/**
 * 格式化日期
 * @param date 日期对象或日期字符串
 * @returns 格式化后的日期字符串
 */
export function formatDate(date: Date | string | number): string {
  let d: Date;
  if (!date) {
    return "-";
  }

  if (typeof date === "number") {
    // 如果是秒级时间戳（10位），转为毫秒
    d = new Date(date < 1e12 ? date * 1000 : date);
  } else if (typeof date === "string") {
    d = new Date(date);
  } else {
    d = date;
  }

  return d.toLocaleDateString("zh-CN", {
    year: "numeric",
    month: "long",
    day: "numeric",
  });
}

export function formatDateTime(date: Date | string | number): string {
  let d: Date;
  if (!date) {
    return "-";
  }
  if (typeof date === "number") {
    // 如果是秒级时间戳（10位），转为毫秒
    d = new Date(date < 1e12 ? date * 1000 : date);
  } else if (typeof date === "string") {
    d = new Date(date);
  } else {
    d = date;
  }

  return d.toLocaleString("zh-CN", {
    year: "numeric",
    month: "2-digit",
    day: "2-digit",
    hour: "2-digit",
    minute: "2-digit",
    second:"2-digit"
  });
}

/**
 * 截断文本
 * @param text 要截断的文本
 * @param length 最大长度
 * @returns 截断后的文本
 */
export function truncateText(text: string, length: number): string {
  if (text.length <= length) return text;
  return text.slice(0, length) + "...";
}

/**
 * 生成随机ID
 * @returns 随机ID字符串
 */
export function generateId(): string {
  return Math.random().toString(36).substring(2, 9);
}

/**
 * 延迟函数
 * @param ms 延迟毫秒数
 * @returns Promise
 */
export function delay(ms: number): Promise<void> {
  return new Promise((resolve) => setTimeout(resolve, ms));
}
