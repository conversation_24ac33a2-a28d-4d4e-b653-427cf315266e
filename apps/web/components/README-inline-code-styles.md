# 内联代码样式优化

## 概述

优化了 MarkdownRenderer 组件中的内联代码样式，使其更加美观、易读，并与整体设计系统保持一致。

## 样式特性

### 🎨 视觉设计
- **背景色**: 使用 `bg-muted` 提供柔和的背景
- **边框**: 添加 `border-border/50` 提供微妙的边框效果
- **圆角**: `rounded-md` 提供现代化的圆角设计
- **字体**: 使用 `font-mono` 确保等宽字体显示

### 📏 尺寸和间距
- **内边距**: `px-[0.3rem] py-[0.2rem]` 提供合适的内部间距
- **字体大小**: `text-sm` 确保可读性
- **字体粗细**: `font-medium` 提供适当的视觉重量

### 🎯 交互效果
- **悬停效果**: `hover:bg-muted/80` 提供微妙的交互反馈
- **过渡动画**: `transition-colors duration-200` 平滑的颜色过渡

### 🌙 深色模式支持
- 自动适配深色模式，使用 `text-foreground` 确保文字颜色正确

## 代码实现

```typescript
// 内联代码样式
<code 
  {...props} 
  className={cn(
    "relative rounded-md bg-muted px-[0.3rem] py-[0.2rem] font-mono text-sm font-medium",
    "border border-border/50",
    "text-foreground",
    "before:content-[''] after:content-['']",
    "transition-colors duration-200",
    "hover:bg-muted/80",
    className
  )}
>
  {/* 内联代码中的引用不处理，保持原样 */}
  {children}
</code>
```

## 使用示例

在 Markdown 中使用内联代码：

```markdown
使用 `npm install` 安装依赖包
React Hook: `useState`, `useEffect`, `useCallback`
CSS 类名: `bg-primary`, `text-foreground`, `hover:bg-muted`
文件路径: `/apps/web/components/markdown-renderer.tsx`
```

## 与引用系统的集成

- ✅ **引用保护**: 内联代码中的引用格式（如 `##1$$`）不会被处理
- ✅ **样式一致**: 与其他 Markdown 元素的样式保持一致
- ✅ **可访问性**: 保持良好的对比度和可读性

## 技术细节

### Tailwind CSS 类说明
- `relative`: 为伪元素提供定位上下文
- `rounded-md`: 中等圆角 (6px)
- `bg-muted`: 使用设计系统的静音背景色
- `px-[0.3rem] py-[0.2rem]`: 精确的内边距控制
- `font-mono`: 等宽字体族
- `text-sm`: 小号文字 (14px)
- `font-medium`: 中等字体粗细 (500)
- `border border-border/50`: 50% 透明度的边框
- `text-foreground`: 前景文字颜色
- `transition-colors duration-200`: 200ms 颜色过渡
- `hover:bg-muted/80`: 悬停时 80% 透明度的背景

### 设计原则
1. **一致性**: 与整体设计系统保持一致
2. **可读性**: 确保代码文本清晰易读
3. **可访问性**: 满足 WCAG 对比度要求
4. **响应式**: 在不同设备上都有良好表现
5. **性能**: 使用 CSS 类而非内联样式，便于优化
