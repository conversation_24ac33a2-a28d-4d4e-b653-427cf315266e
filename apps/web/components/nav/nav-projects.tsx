"use client"

import {
  type LucideIcon,
} from "lucide-react"

import {
  SidebarGroup,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from "@ragtop-web/ui/components/sidebar"
import Link from "next/link"
import { usePathname } from "next/navigation"
import { cn } from "@ragtop-web/ui/lib/utils"


export function NavProjects({
  projects,
  title,
}: {
  projects: {
    name: string
    url: string
    icon: LucideIcon
  }[],
  title: string
}) {
  const pathname = usePathname()

  return (
    <SidebarGroup className="group-data-[collapsible=icon]:hidden">
      <SidebarGroupLabel>{title}</SidebarGroupLabel>
      <SidebarMenu>
        {projects.map((item) => {
          const isActive = pathname === item.url
          return (<SidebarMenuItem key={item.name}>
            <SidebarMenuButton asChild className={cn(
              "w-full justify-start gap-2",
              isActive && "bg-muted font-medium"
            )}>
              <Link href={item.url}>
                <item.icon />
                <span>{item.name}</span>
              </Link>
            </SidebarMenuButton>
          </SidebarMenuItem>)
        })}
      </SidebarMenu>
    </SidebarGroup>
  )
}
